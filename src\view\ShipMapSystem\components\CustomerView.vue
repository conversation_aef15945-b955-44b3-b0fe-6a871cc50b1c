<template>
    <div class="customer-view">
        <div class="table-card" style="height: calc(100vh - 85px);">
            <div class="card-title">
                <div class="title-text">
                    <i class="fas fa-list-ul"></i> 客户列表
                    <span class="title-tip">点击查看客户详情</span>
                </div>
            </div>
            <div class="customer-list">
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>正在加载客户数据...</span>
                </div>

                <!-- 客户列表 -->
                <template v-else-if="customerList.length > 0">
                    <div
                        v-for="(item, index) in customerList"
                        :key="index"
                        class="customer-item"
                        :class="{ 'selected': selectedCustomer && selectedCustomer.cargoCompanyName === item.cargoCompanyName }"
                        @click="handleCustomerClick(item)"
                    >
                        <div class="customer-name">
                            <i class="fas fa-building customer-icon"></i>
                            {{ item.cargoCompanyName }}
                        </div>
                        <div class="customer-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </template>

                <!-- 无数据状态 -->
                <div v-else class="no-data-state">
                    <i class="fas fa-inbox"></i>
                    <span>暂无客户数据</span>
                    <p>请尝试调整筛选条件</p>
                </div>
            </div>
        </div>
        <div class="table-card detail-card" style="height: calc(100vh - 85px);">
            <div class="card-title">
                <div class="title-text">
                    <i class="fas fa-info-circle"></i> 客户详情
                </div>

            </div>
            <div class="customer-detail" v-if="selectedCustomer">
                <!-- 客户名称标题 -->
                <div class="detail-header">
                    <h3 class="customer-title">
                        <i class="fas fa-building"></i>
                        {{ selectedCustomer.cargoCompanyName }}
                    </h3>
                </div>

                <!-- 货物统计信息 -->
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-chart-bar"></i>
                        货物统计
                    </div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">装货总量</div>
                            <div class="info-value">{{ selectedCustomer.loadUnitAmount || '0' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">卸货总量</div>
                            <div class="info-value">{{ selectedCustomer.unloadUnitAmount || '0' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">货损量</div>
                            <div class="info-value danger">{{ selectedCustomer.goodsLossAmount || '0' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">货损率</div>
                            <div class="info-value danger">{{ selectedCustomer.goodsLossRate || '0' }}‰</div>
                        </div>
                    </div>
                    <div class="info-list">
                        <div class="info-item-full">
                            <div class="info-label">货品类型</div>
                            <div class="info-value-text">{{ selectedCustomer.cargoNames || '暂无数据' }}</div>
                        </div>
                    </div>
                </div>

                <!-- 运营统计 -->
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-ship"></i>
                        运营统计
                    </div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">总锚泊时长</div>
                            <div class="info-value">{{ selectedCustomer.totalAnchorTime || '0' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">航次数</div>
                            <div class="info-value">{{ selectedCustomer.voyageCount || '0' }}</div>
                        </div>
                    </div>
                    <div class="info-list">
                        <div class="info-item-full">
                            <div class="info-label">承运船舶</div>
                            <div class="info-value-text">{{ selectedCustomer.shipNames || '暂无数据' }}</div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- 未选择客户时的提示 -->
            <div class="no-selection" v-else>
                <div class="no-selection-content">
                    <i class="fas fa-hand-pointer"></i>
                    <p>请从左侧列表中选择一个客户查看详情</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios'
import config from '@/config'
export default {
    name: 'CustomerView',
    data() {
        return {
            customerList: [],
            selectedCustomer: null,
            loading: false
        };
    },
    mounted() {
        console.log(1111111111);
        
        this.getCustomerList()
    },
    methods: {
        async getCustomerList() {
            try {
                this.loading = true;
                const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.multi_dev : config.baseUrl.multi_pro
                let res = await axios.post(baseUrl + '/multi/source/api/voyage/cargo-company-stats', {
                    startDate: "2024-01-01",
                    endDate: "2024-12-31",
                    page: 1,
                    size: 1000,
                }, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('MultiDataToken')}`,
                        'Content-Type': 'application/json'
                    }
                })

                this.customerList = res.data.data.records
                this.selectedCustomer = this.customerList[0]
            } catch (error) {
                console.error('获取客户列表失败:', error);
                this.$message.error('获取客户列表失败，请重试');
            } finally {
                this.loading = false;
            }
        },
        handleCustomerClick(customer) {
            this.selectedCustomer = customer;
            console.log('选中客户:', customer);
            // 这里可以添加更多的客户详情处理逻辑
        }
    }
}
</script>
<style lang="less" scoped>
.customer-view {
    padding: 16px 16px;
    display: flex;
    gap: 16px;

    .table-card {
        background: linear-gradient(140deg, #0c294b, #072040);
        border-radius: 6px;
        height: 100%;
        padding: 10px 0px;
        border: 1px solid #0e3461;
        width: 35%;
        display: flex;
        flex-direction: column;

        .card-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #1ec0bb;
            border-bottom: 1px solid #1a3d6a;
            padding-bottom: 10px;
            padding-left: 15px;
            padding-right: 15px;
            display: flex;
            align-items: center;
            min-height: 24px;
            justify-content: space-between;

            .title-tip {
                font-size: 14px;
                color: #8eb0b6;
            }
        }



        .card-title i {
            margin-right: 6px;
            color: #1ec0bb;
        }

        .customer-list {
            flex: 1 1 0%;
            overflow: auto;
            padding: 8px 15px;

            .customer-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 16px;
                margin-bottom: 12px;
                min-height: 52px;
                background: rgba(30, 192, 187, 0.08);
                border: 1px solid rgba(30, 192, 187, 0.2);
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;

                &:hover {
                    background: rgba(30, 192, 187, 0.15);
                    border-color: rgba(30, 192, 187, 0.4);
                    transform: translateX(2px);
                }

                &:active {
                    transform: translateX(2px);
                    background: rgba(30, 192, 187, 0.2);
                }

                .customer-name {
                    display: flex;
                    align-items: center;
                    color: #e8f4f8;
                    font-size: 15px;
                    font-weight: 500;
                    flex: 1;
                    line-height: 1.5;

                    .customer-icon {
                        color: #1ec0bb;
                        margin-right: 10px;
                        font-size: 16px;
                        width: 16px;
                        text-align: center;
                    }
                }

                .customer-arrow {
                    color: #1ec0bb;
                    font-size: 12px;
                    opacity: 0.7;
                    transition: all 0.3s ease;
                }

                &:hover .customer-arrow {
                    opacity: 1;
                    transform: translateX(2px);
                }

                // 添加一个微妙的左边框装饰
                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 3px;
                    background: linear-gradient(to bottom, #1ec0bb, #0ea5a0);
                    opacity: 0;
                    transition: opacity 0.3s ease;
                }

                &:hover::before {
                    opacity: 1;
                }

                // 选中状态样式
                &.selected {
                    background: rgba(30, 192, 187, 0.25);
                    border-color: #1ec0bb;
                    box-shadow: 0 0 15px rgba(30, 192, 187, 0.3);

                    .customer-name {
                        color: #ffffff;
                        font-weight: 600;
                    }

                    .customer-arrow {
                        opacity: 1;
                        color: #ffffff;
                    }

                    &::before {
                        opacity: 1;
                        width: 4px;
                        background: #1ec0bb;
                    }
                }
            }

            // 自定义滚动条样式
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(26, 61, 106, 0);
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(30, 192, 187, 0.5);
                border-radius: 3px;

                &:hover {
                    background: rgba(30, 192, 187, 0.7);
                }
            }

            .loading-state, .no-data-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 40px 20px;
                color: #8eb0b6;
                text-align: center;

                i {
                    font-size: 32px;
                    color: rgba(30, 192, 187, 0.5);
                    margin-bottom: 12px;
                }

                span {
                    font-size: 14px;
                    margin-bottom: 8px;
                }

                p {
                    font-size: 12px;
                    margin: 0;
                    opacity: 0.8;
                }
            }

            .loading-state {
                i {
                    animation: spin 1s linear infinite;
                }
            }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    }

    .detail-card {
        width: 65%;

        .customer-detail {
            flex: 1;
            overflow-y: auto;
            padding: 0 15px;

            .detail-header {
                margin-bottom: 24px;
                padding-bottom: 16px;
                border-bottom: 2px solid rgba(30, 192, 187, 0.3);

                .customer-title {
                    color: #1ec0bb;
                    font-size: 22px;
                    font-weight: 600;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 12px;

                    i {
                        font-size: 20px;
                    }
                }
            }

            .detail-section {
                margin-bottom: 28px;

                .section-title {
                    color: #e8f4f8;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 16px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid rgba(30, 192, 187, 0.2);

                    i {
                        color: #1ec0bb;
                        font-size: 14px;
                    }
                }

                .info-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 16px;
                }

                .info-list {
                    display: flex;
                    flex-direction: column;
                    gap: 16px;
                }

                .info-item {
                    background: rgba(30, 192, 187, 0.08);
                    border: 1px solid rgba(30, 192, 187, 0.2);
                    border-radius: 8px;
                    padding: 16px;
                    transition: all 0.3s ease;

                    &:hover {
                        background: rgba(30, 192, 187, 0.12);
                        border-color: rgba(30, 192, 187, 0.3);
                    }

                    .info-label {
                        color: #8eb0b6;
                        font-size: 12px;
                        margin-bottom: 8px;
                        font-weight: 500;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }

                    .info-value {
                        color: #e8f4f8;
                        font-size: 18px;
                        font-weight: 600;

                        &.danger {
                            color: #ff6b6b;
                        }
                    }
                }

                .info-item-full {
                    margin-top: 16px;
                    background: rgba(30, 192, 187, 0.08);
                    border: 1px solid rgba(30, 192, 187, 0.2);
                    border-radius: 8px;
                    padding: 16px;
                    transition: all 0.3s ease;

                    &:hover {
                        background: rgba(30, 192, 187, 0.12);
                        border-color: rgba(30, 192, 187, 0.3);
                    }

                    .info-label {
                        color: #8eb0b6;
                        font-size: 12px;
                        margin-bottom: 8px;
                        font-weight: 500;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }

                    .info-value-text {
                        color: #e8f4f8;
                        font-size: 14px;
                        line-height: 1.6;
                        word-break: break-all;
                    }
                }
            }

            // 自定义滚动条
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(26, 61, 106, 0);
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(30, 192, 187, 0.5);
                border-radius: 3px;

                &:hover {
                    background: rgba(30, 192, 187, 0.7);
                }
            }
        }

        .no-selection {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;

            .no-selection-content {
                text-align: center;
                color: #8eb0b6;

                i {
                    font-size: 48px;
                    color: rgba(30, 192, 187, 0.5);
                    margin-bottom: 16px;
                    display: block;
                }

                p {
                    font-size: 16px;
                    margin: 0;
                    line-height: 1.5;
                }
            }
        }
    }
}
</style>
